# PDF to HTML Conversion in Python
# Multiple approaches for different use cases

import os
from pathlib import Path

# Method 1: Using pdfplumber (Best for text extraction with some formatting)
def pdf_to_html_pdfplumber(pdf_path, output_path):
    """
    Convert PDF to HTML using pdfplumber
    Good for: Text extraction with basic formatting
    """
    try:
        import pdfplumber
    except ImportError:
        print("Install pdfplumber: pip install pdfplumber")
        return
    
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>PDF Content</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            .page { margin-bottom: 50px; border-bottom: 1px solid #ccc; padding-bottom: 30px; }
            .page-number { color: #666; font-size: 12px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
    """
    
    with pdfplumber.open(pdf_path) as pdf:
        for i, page in enumerate(pdf.pages):
            text = page.extract_text()
            if text:
                # Convert line breaks to HTML
                text = text.replace('\n\n', '</p><p>')
                text = text.replace('\n', '<br>')
                
                html_content += f"""
                <div class="page">
                    <div class="page-number">Page {i + 1}</div>
                    <p>{text}</p>
                </div>
                """
    
    html_content += "</body></html>"
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML saved to: {output_path}")

# Method 2: Using PyMuPDF (fitz) - More advanced layout preservation
def pdf_to_html_pymupdf(pdf_path, output_path):
    """
    Convert PDF to HTML using PyMuPDF
    Good for: Better layout and formatting preservation
    """
    try:
        import fitz  # PyMuPDF
    except ImportError:
        print("Install PyMuPDF: pip install PyMuPDF")
        return
    
    doc = fitz.open(pdf_path)
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>PDF Content</title>
        <style>
            body { margin: 20px; font-family: Arial, sans-serif; }
            .page { margin-bottom: 40px; }
            .text-block { margin-bottom: 10px; }
        </style>
    </head>
    <body>
    """
    
    for page_num in range(doc.page_count):
        page = doc[page_num]
        
        # Get text with formatting information
        text_dict = page.get_text("dict")
        
        html_content += f'<div class="page"><h3>Page {page_num + 1}</h3>'
        
        for block in text_dict["blocks"]:
            if "lines" in block:  # Text block
                block_text = ""
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"]
                        font_size = span["size"]
                        font_flags = span["flags"]
                        
                        # Apply formatting based on font properties
                        style = f"font-size: {font_size}px;"
                        if font_flags & 2**4:  # Bold
                            style += " font-weight: bold;"
                        if font_flags & 2**1:  # Italic
                            style += " font-style: italic;"
                        
                        block_text += f'<span style="{style}">{text}</span>'
                    block_text += "<br>"
                
                html_content += f'<div class="text-block">{block_text}</div>'
        
        html_content += '</div>'
    
    html_content += "</body></html>"
    doc.close()
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML saved to: {output_path}")

# Method 3: Using pdfminer - Low-level control
def pdf_to_html_pdfminer(pdf_path, output_path):
    """
    Convert PDF to HTML using pdfminer
    Good for: Detailed control over extraction process
    """
    try:
        from pdfminer.high_level import extract_text_to_fp
        from pdfminer.layout import LAParams
        from io import StringIO
    except ImportError:
        print("Install pdfminer.six: pip install pdfminer.six")
        return
    
    output_string = StringIO()
    
    with open(pdf_path, 'rb') as file:
        extract_text_to_fp(
            file, 
            output_string,
            laparams=LAParams(),
            output_type='html',
            codec=None
        )
    
    html_content = output_string.getvalue()
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML saved to: {output_path}")

# Method 4: Advanced conversion with pdf2htmlEX (requires external tool)
def pdf_to_html_pdf2htmlex(pdf_path, output_dir):
    """
    Convert PDF to HTML using pdf2htmlEX (external tool)
    Best for: Pixel-perfect HTML conversion with CSS
    Note: Requires pdf2htmlEX to be installed on system
    """
    import subprocess
    
    try:
        # Check if pdf2htmlEX is installed
        subprocess.run(["pdf2htmlEX", "--version"], 
                      capture_output=True, check=True)
        
        # Convert PDF
        output_name = Path(pdf_path).stem + ".html"
        subprocess.run([
            "pdf2htmlEX",
            "--zoom", "1.3",
            "--dest-dir", output_dir,
            pdf_path,
            output_name
        ], check=True)
        
        print(f"HTML saved to: {os.path.join(output_dir, output_name)}")
        
    except subprocess.CalledProcessError:
        print("pdf2htmlEX not found. Install it from: https://github.com/coolwanglu/pdf2htmlEX")
    except FileNotFoundError:
        print("pdf2htmlEX not found in PATH")

# Method 5: Batch conversion utility
def batch_convert_pdfs(input_dir, output_dir, method="pdfplumber"):
    """
    Convert multiple PDFs to HTML
    """
    Path(output_dir).mkdir(exist_ok=True)
    
    methods = {
        "pdfplumber": pdf_to_html_pdfplumber,
        "pymupdf": pdf_to_html_pymupdf,
        "pdfminer": pdf_to_html_pdfminer
    }
    
    converter = methods.get(method, pdf_to_html_pdfplumber)
    
    for pdf_file in Path(input_dir).glob("*.pdf"):
        output_file = Path(output_dir) / f"{pdf_file.stem}.html"
        print(f"Converting: {pdf_file.name}")
        converter(str(pdf_file), str(output_file))

# Example usage
if __name__ == "__main__":
    # Check for PDF files in current directory
    pdf_files = list(Path(".").glob("*.pdf"))

    if not pdf_files:
        print("No PDF files found in current directory.")
        pdf_file = input("Enter the path to your PDF file: ").strip()
        if not os.path.exists(pdf_file):
            print(f"Error: File '{pdf_file}' not found!")
            exit(1)
    else:
        print("Found PDF files:")
        for i, pdf in enumerate(pdf_files, 1):
            print(f"{i}. {pdf.name}")

        if len(pdf_files) == 1:
            pdf_file = str(pdf_files[0])
            print(f"Using: {pdf_file}")
        else:
            try:
                choice_num = int(input("Select PDF file number: ")) - 1
                pdf_file = str(pdf_files[choice_num])
            except (ValueError, IndexError):
                print("Invalid selection!")
                exit(1)

    # Generate output filename
    output_html = Path(pdf_file).stem + ".html"

    print(f"\nInput: {pdf_file}")
    print(f"Output: {output_html}")

    print("\nChoose conversion method:")
    print("1. pdfplumber (simple text extraction)")
    print("2. PyMuPDF (better formatting)")
    print("3. pdfminer (detailed control)")
    print("4. pdf2htmlEX (pixel-perfect, requires external tool)")

    choice = input("Enter choice (1-4): ")

    try:
        if choice == "1":
            pdf_to_html_pdfplumber(pdf_file, output_html)
        elif choice == "2":
            pdf_to_html_pymupdf(pdf_file, output_html)
        elif choice == "3":
            pdf_to_html_pdfminer(pdf_file, output_html)
        elif choice == "4":
            pdf_to_html_pdf2htmlex(pdf_file, "output")
        else:
            print("Invalid choice!")
    except Exception as e:
        print(f"Error during conversion: {e}")

    # Batch conversion example
    # batch_convert_pdfs("input_pdfs", "output_html", "pymupdf")